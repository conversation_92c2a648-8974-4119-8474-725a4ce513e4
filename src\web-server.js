import { H3, serve } from 'h3';
import { promises as fs } from 'fs';
import path from 'path';
import { authMiddleware } from './middlewares/auth.js';
import { corsMiddleware } from './middlewares/cors.js';
import { healthHandler } from './routes/health.js';
import { initHandler } from './routes/init.js';
import { resetHandler } from './routes/reset.js';
import { getScreenshotsHandler, getScreenshotHandler, screenshotViewHandler } from './routes/screenshots.js';
import config from './config.js';

const app = new H3();

// 日志函数
function info(message) {
    console.log(`[INFO] ${new Date().toISOString()} - ${message}`);
}

// 全局 CORS 中间件
app.use(corsMiddleware);
app.use(authMiddleware);
// --- API 端点 ---
// 健康检查端点
app.get('/health', healthHandler);

// 账号重置端点
app.post('/reset-account', resetHandler);
app.post('/init-account', initHandler);

// 截图相关端点
app.get('/screenshots', screenshotViewHandler);
app.get('/api/screenshot', getScreenshotHandler);

// 主页路由
app.get('/', async (event) => {
    try {
        const indexPath = path.join(process.cwd(), 'public', 'index.html');
        const content = await fs.readFile(indexPath, 'utf-8');
        event.node.res.setHeader('Content-Type', 'text/html; charset=utf-8');
        return content;
    } catch (error) {
        return '<h1>Welcome to Microsoft Reward Account Reset</h1><p><a href="/health">Health Check</a> | <a href="/screenshots">Screenshots</a></p>';
    }
});


// 启动服务器
const port = config.server.port;
const host = config.server.host;
serve(app, { port, host });
info(`🚀 服务器运行在 http://${host}:${port}`);
info(`🏠 管理面板: http://${host}:${port}/`);
info(`📋 健康检查: http://${host}:${port}/health`);
info(`🖼️ 错误截图: http://${host}:${port}/screenshots`);
info(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
