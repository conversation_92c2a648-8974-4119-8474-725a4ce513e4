<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Microsoft Reward Account Reset</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .nav-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            text-decoration: none;
            color: #333;
            border: 2px solid transparent;
            transition: all 0.2s;
        }
        .nav-item:hover {
            border-color: #007bff;
            background: #e3f2fd;
            transform: translateY(-2px);
        }
        .nav-item h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .nav-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .status {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status.online {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Microsoft Reward Account Reset</h1>

        <div class="status online">
            <strong>✅ 服务运行中</strong>
        </div>

        <div class="nav-grid">
            <a href="/health" class="nav-item">
                <h3>📋 健康检查</h3>
                <p>查看服务状态和系统信息</p>
            </a>

            <a href="/screenshots" class="nav-item">
                <h3>🖼️ 错误截图</h3>
                <p>查看账号重置失败时的错误截图</p>
            </a>

            <div class="nav-item" style="opacity: 0.6;">
                <h3>🔧 API 端点</h3>
                <p>POST /reset-account<br>POST /init-account</p>
            </div>
        </div>

        <div class="footer">
            <p>Microsoft Reward Account Reset Service</p>
            <p>Powered by H3 + Playwright</p>
        </div>
    </div>

    <script>
        // 检查服务状态
        fetch('/health')
            .then(response => response.json())
            .then(data => {
                const statusEl = document.querySelector('.status');
                if (data.status === 'ok') {
                    statusEl.className = 'status online';
                    statusEl.innerHTML = '<strong>✅ 服务运行中</strong>';
                } else {
                    statusEl.className = 'status offline';
                    statusEl.innerHTML = '<strong>❌ 服务异常</strong>';
                }
            })
            .catch(() => {
                const statusEl = document.querySelector('.status');
                statusEl.className = 'status offline';
                statusEl.innerHTML = '<strong>❌ 无法连接到服务</strong>';
            });
    </script>
</body>
</html>